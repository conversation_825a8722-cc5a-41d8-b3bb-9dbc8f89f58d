import React from "react";
import { Code, Smartphone, Zap, Users } from "lucide-react";

const About: React.FC = () => {
  const highlights = [
    {
      icon: <Code className="w-8 h-8" />,
      title: "Clean Code",
      description:
        "Writing maintainable, scalable, and well-documented code following best practices.",
    },
    {
      icon: <Smartphone className="w-8 h-8" />,
      title: "Cross-Platform",
      description:
        "Expertise in Flutter for building apps that work seamlessly on iOS and Android.",
    },
    {
      icon: <Zap className="w-8 h-8" />,
      title: "Performance",
      description:
        "Optimizing app performance for smooth user experiences and fast load times.",
    },
    {
      icon: <Users className="w-8 h-8" />,
      title: "User-Centric",
      description:
        "Focusing on UI/UX best practices to create intuitive and engaging mobile apps.",
    },
  ];

  return (
    <section id="about" className="section-padding bg-white">
      <div className="container-custom">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-slate-900 mb-6">
              About <span className="text-gradient">Me</span>
            </h2>
            <p className="text-lg text-slate-600 max-w-3xl mx-auto">
              Passionate Flutter developer dedicated to creating exceptional
              mobile experiences
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Column - Text Content */}
            <div className="space-y-6">
              <h3 className="text-2xl md:text-3xl font-bold text-slate-900 mb-6">
                Professional Summary
              </h3>

              <p className="text-lg text-secondary-600 leading-relaxed">
                Results-driven Flutter Developer with{" "}
                <strong>4-5 years of experience</strong> in building
                high-performance, cross-platform mobile applications. I
                specialize in creating scalable and maintainable mobile
                solutions that deliver exceptional user experiences.
              </p>

              <p className="text-lg text-secondary-600 leading-relaxed">
                My expertise spans across the entire mobile development
                lifecycle, from concept and design to deployment and
                maintenance. I'm proficient in{" "}
                <strong>
                  Dart, Flutter framework, state management, Firebase, and
                  RESTful APIs
                </strong>
                , with a strong focus on clean code architecture and performance
                optimization.
              </p>

              <p className="text-lg text-secondary-600 leading-relaxed">
                I'm passionate about staying up-to-date with the latest mobile
                development trends and technologies, ensuring that the
                applications I build are not only functional but also
                future-proof and scalable.
              </p>

              {/* Experience Stats */}
              <div className="grid grid-cols-2 gap-6 pt-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-gradient mb-2">
                    4-5
                  </div>
                  <div className="text-secondary-600">Years Experience</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-gradient mb-2">
                    7+
                  </div>
                  <div className="text-secondary-600">Projects Delivered</div>
                </div>
              </div>
            </div>

            {/* Right Column - Highlights Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              {highlights.map((highlight, index) => (
                <div
                  key={index}
                  className="bg-gray-50 p-6 rounded-xl card-hover"
                >
                  <div className="text-primary-600 mb-4">{highlight.icon}</div>
                  <h4 className="text-xl font-semibold text-secondary-900 mb-3">
                    {highlight.title}
                  </h4>
                  <p className="text-secondary-600">{highlight.description}</p>
                </div>
              ))}
            </div>
          </div>

          {/* Additional Info */}
          <div className="mt-16 bg-gradient-to-r from-primary-50 to-blue-50 rounded-2xl p-8 md:p-12">
            <div className="text-center">
              <h3 className="text-2xl md:text-3xl font-bold text-secondary-900 mb-6">
                What Drives Me
              </h3>
              <p className="text-lg text-secondary-600 max-w-4xl mx-auto leading-relaxed">
                I believe that great mobile applications are born from the
                perfect blend of technical excellence and user-centered design.
                My goal is to create apps that not only function flawlessly but
                also provide intuitive and delightful user experiences. I'm
                constantly learning and adapting to new technologies to ensure I
                can deliver the best possible solutions for every project.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
