import React from 'react';
import { useScrollAnimation, fadeInUp } from '../hooks/useScrollAnimation';

interface AnimatedSectionProps {
  children: React.ReactNode;
  className?: string;
  delay?: number;
  animation?: 'fadeInUp' | 'fadeInLeft' | 'fadeInRight' | 'fadeIn' | 'scaleIn';
  threshold?: number;
}

const AnimatedSection: React.FC<AnimatedSectionProps> = ({
  children,
  className = '',
  delay = 0,
  animation = 'fadeInUp',
  threshold = 0.1,
}) => {
  const { ref, isVisible } = useScrollAnimation({ threshold });

  const getAnimationStyle = () => {
    switch (animation) {
      case 'fadeInUp':
        return {
          opacity: isVisible ? 1 : 0,
          transform: isVisible ? 'translateY(0)' : 'translateY(30px)',
          transition: `all 0.6s ease-out ${delay}s`,
        };
      case 'fadeInLeft':
        return {
          opacity: isVisible ? 1 : 0,
          transform: isVisible ? 'translateX(0)' : 'translateX(-30px)',
          transition: `all 0.6s ease-out ${delay}s`,
        };
      case 'fadeInRight':
        return {
          opacity: isVisible ? 1 : 0,
          transform: isVisible ? 'translateX(0)' : 'translateX(30px)',
          transition: `all 0.6s ease-out ${delay}s`,
        };
      case 'fadeIn':
        return {
          opacity: isVisible ? 1 : 0,
          transition: `opacity 0.6s ease-out ${delay}s`,
        };
      case 'scaleIn':
        return {
          opacity: isVisible ? 1 : 0,
          transform: isVisible ? 'scale(1)' : 'scale(0.9)',
          transition: `all 0.6s ease-out ${delay}s`,
        };
      default:
        return fadeInUp(isVisible, delay);
    }
  };

  return (
    <div
      ref={ref}
      className={className}
      style={getAnimationStyle()}
    >
      {children}
    </div>
  );
};

export default AnimatedSection;
