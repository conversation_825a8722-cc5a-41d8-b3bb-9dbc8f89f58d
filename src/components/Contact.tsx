import React, { useState } from "react";
import {
  Mail,
  Phone,
  MapPin,
  Github,
  Linkedin,
  Send,
  Download,
} from "lucide-react";
import AnimatedSection from "./AnimatedSection";

const Contact: React.FC = () => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    subject: "",
    message: "",
  });

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Create mailto link with form data
    const subject = encodeURIComponent(formData.subject || "Portfolio Contact");
    const body = encodeURIComponent(
      `Name: ${formData.name}\nEmail: ${formData.email}\n\nMessage:\n${formData.message}`
    );
    window.location.href = `mailto:<EMAIL>?subject=${subject}&body=${body}`;
  };

  const contactInfo = [
    {
      icon: <Mail className="w-6 h-6" />,
      label: "Email",
      value: "<EMAIL>",
      href: "mailto:<EMAIL>",
    },
    {
      icon: <Phone className="w-6 h-6" />,
      label: "Phone",
      value: "+234 ************",
      href: "tel:+2349167392396",
    },
    {
      icon: <MapPin className="w-6 h-6" />,
      label: "Location",
      value: "Nigeria",
      href: null,
    },
  ];

  const socialLinks = [
    {
      icon: <Github className="w-6 h-6" />,
      label: "GitHub",
      href: "https://github.com/obiaderi",
      color: "hover:text-gray-900",
    },
    {
      icon: <Linkedin className="w-6 h-6" />,
      label: "LinkedIn",
      href: "https://www.linkedin.com/in/edobor-obiaderi-9933b0241",
      color: "hover:text-blue-600",
    },
  ];

  return (
    <section id="contact" className="section-padding bg-white dark:bg-gray-900">
      <div className="container-custom">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <AnimatedSection className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-slate-900 dark:text-white mb-6">
              Get In <span className="text-gradient">Touch</span>
            </h2>
            <p className="text-lg text-slate-600 dark:text-gray-300 max-w-3xl mx-auto">
              Ready to start your next mobile project? Let's discuss how I can
              help bring your ideas to life
            </p>
          </AnimatedSection>

          <div className="grid lg:grid-cols-2 gap-12">
            {/* Contact Information */}
            <AnimatedSection
              animation="fadeInLeft"
              delay={0.2}
              className="space-y-8"
            >
              <div>
                <h3 className="text-2xl font-bold text-secondary-900 dark:text-white mb-6">
                  Let's Connect
                </h3>
                <p className="text-lg text-secondary-600 dark:text-gray-300 mb-8 leading-relaxed">
                  I'm always excited to discuss new opportunities, collaborate
                  on interesting projects, or simply chat about mobile
                  development. Feel free to reach out through any of the
                  channels below.
                </p>
              </div>

              {/* Contact Details */}
              <div className="space-y-6">
                {contactInfo.map((info, index) => (
                  <div key={index} className="flex items-center">
                    <div className="bg-primary-100 dark:bg-blue-900 p-3 rounded-full mr-4">
                      <div className="text-primary-600 dark:text-blue-400">
                        {info.icon}
                      </div>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-secondary-500 dark:text-gray-400 uppercase tracking-wide">
                        {info.label}
                      </p>
                      {info.href ? (
                        <a
                          href={info.href}
                          className="text-lg text-secondary-900 dark:text-white hover:text-primary-600 dark:hover:text-blue-400 transition-colors duration-200"
                        >
                          {info.value}
                        </a>
                      ) : (
                        <p className="text-lg text-secondary-900 dark:text-white">
                          {info.value}
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              {/* Social Links */}
              <div>
                <h4 className="text-lg font-semibold text-secondary-900 dark:text-white mb-4">
                  Follow Me
                </h4>
                <div className="flex space-x-4">
                  {socialLinks.map((social, index) => (
                    <a
                      key={index}
                      href={social.href}
                      target="_blank"
                      rel="noopener noreferrer"
                      className={`p-3 bg-gray-100 dark:bg-gray-800 rounded-full transition-all duration-300 hover:bg-gray-200 dark:hover:bg-gray-700 hover:-translate-y-1 text-secondary-600 dark:text-gray-300 ${social.color}`}
                      title={social.label}
                    >
                      {social.icon}
                    </a>
                  ))}
                </div>
              </div>

              {/* Resume Download */}
              <div className="bg-gradient-to-r from-primary-50 to-blue-50 dark:from-gray-800 dark:to-gray-700 rounded-xl p-6">
                <h4 className="text-lg font-semibold text-secondary-900 dark:text-white mb-3">
                  Download Resume
                </h4>
                <p className="text-secondary-600 dark:text-gray-300 mb-4">
                  Get a detailed overview of my experience, skills, and
                  projects.
                </p>
                <a
                  href="#resume"
                  onClick={(e) => {
                    e.preventDefault();
                    const resumeSection = document.querySelector("#resume");
                    if (resumeSection) {
                      resumeSection.scrollIntoView({ behavior: "smooth" });
                    }
                  }}
                  className="btn-primary"
                >
                  <Download className="w-5 h-5" />
                  View Resume
                </a>
              </div>
            </AnimatedSection>

            {/* Contact Form */}
            <AnimatedSection
              animation="fadeInRight"
              delay={0.4}
              className="bg-gray-50 dark:bg-gray-800 rounded-xl p-8"
            >
              <h3 className="text-2xl font-bold text-secondary-900 dark:text-white mb-6">
                Send a Message
              </h3>

              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <label
                      htmlFor="name"
                      className="block text-sm font-medium text-secondary-700 dark:text-gray-300 mb-2"
                    >
                      Name *
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      required
                      value={formData.name}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200"
                      placeholder="Your name"
                    />
                  </div>

                  <div>
                    <label
                      htmlFor="email"
                      className="block text-sm font-medium text-secondary-700 dark:text-gray-300 mb-2"
                    >
                      Email *
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      required
                      value={formData.email}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                <div>
                  <label
                    htmlFor="subject"
                    className="block text-sm font-medium text-secondary-700 dark:text-gray-300 mb-2"
                  >
                    Subject
                  </label>
                  <input
                    type="text"
                    id="subject"
                    name="subject"
                    value={formData.subject}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200"
                    placeholder="Project discussion, collaboration, etc."
                  />
                </div>

                <div>
                  <label
                    htmlFor="message"
                    className="block text-sm font-medium text-secondary-700 dark:text-gray-300 mb-2"
                  >
                    Message *
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    required
                    rows={6}
                    value={formData.message}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200 resize-none"
                    placeholder="Tell me about your project or how I can help you..."
                  ></textarea>
                </div>

                <button
                  type="submit"
                  className="w-full btn-primary justify-center"
                >
                  <Send className="w-5 h-5" />
                  Send Message
                </button>
              </form>
            </AnimatedSection>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Contact;
