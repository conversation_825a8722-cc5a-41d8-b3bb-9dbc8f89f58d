import React from 'react';
import { GraduationCap, Calendar, MapPin, Award } from 'lucide-react';

const Education: React.FC = () => {
  return (
    <section id="education" className="section-padding bg-gray-50">
      <div className="container-custom">
        <div className="max-w-4xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-secondary-900 mb-6">
              <span className="text-gradient">Education</span>
            </h2>
            <p className="text-lg text-secondary-600 max-w-3xl mx-auto">
              My academic foundation in engineering that supports my technical expertise
            </p>
          </div>

          {/* Education Card */}
          <div className="bg-white rounded-xl shadow-lg overflow-hidden card-hover">
            <div className="p-8 md:p-12">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
                <div className="flex items-center mb-4 md:mb-0">
                  <div className="bg-primary-100 p-4 rounded-full mr-6">
                    <GraduationCap className="w-8 h-8 text-primary-600" />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-secondary-900 mb-2">
                      Bachelor of Science in Engineering
                    </h3>
                    <p className="text-xl text-primary-600 font-semibold">
                      Electrical/Electronics Engineering
                    </p>
                  </div>
                </div>
                
                <div className="bg-primary-50 p-4 rounded-lg">
                  <div className="flex items-center text-primary-700 mb-2">
                    <Calendar className="w-5 h-5 mr-2" />
                    <span className="font-semibold">2021</span>
                  </div>
                  <div className="flex items-center text-primary-700">
                    <MapPin className="w-5 h-5 mr-2" />
                    <span>Benin City, Nigeria</span>
                  </div>
                </div>
              </div>

              <div className="mb-8">
                <h4 className="text-lg font-semibold text-secondary-900 mb-4">
                  University of Benin
                </h4>
                <p className="text-secondary-600 leading-relaxed">
                  Completed a comprehensive engineering program with focus on electrical and electronics 
                  systems. The curriculum provided a strong foundation in problem-solving, analytical 
                  thinking, and technical design principles that directly translate to software development 
                  and mobile application architecture.
                </p>
              </div>

              {/* Key Areas */}
              <div className="grid md:grid-cols-2 gap-6 mb-8">
                <div>
                  <h5 className="font-semibold text-secondary-900 mb-3">Key Areas of Study:</h5>
                  <ul className="space-y-2 text-secondary-600">
                    <li className="flex items-center">
                      <div className="w-2 h-2 bg-primary-500 rounded-full mr-3"></div>
                      Digital Systems & Logic Design
                    </li>
                    <li className="flex items-center">
                      <div className="w-2 h-2 bg-primary-500 rounded-full mr-3"></div>
                      Computer Programming
                    </li>
                    <li className="flex items-center">
                      <div className="w-2 h-2 bg-primary-500 rounded-full mr-3"></div>
                      Systems Analysis & Design
                    </li>
                    <li className="flex items-center">
                      <div className="w-2 h-2 bg-primary-500 rounded-full mr-3"></div>
                      Mathematics & Statistics
                    </li>
                  </ul>
                </div>
                
                <div>
                  <h5 className="font-semibold text-secondary-900 mb-3">Skills Developed:</h5>
                  <ul className="space-y-2 text-secondary-600">
                    <li className="flex items-center">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                      Analytical Problem Solving
                    </li>
                    <li className="flex items-center">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                      Technical Documentation
                    </li>
                    <li className="flex items-center">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                      Project Management
                    </li>
                    <li className="flex items-center">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                      Team Collaboration
                    </li>
                  </ul>
                </div>
              </div>

              {/* Achievement */}
              <div className="bg-gradient-to-r from-primary-50 to-blue-50 rounded-lg p-6">
                <div className="flex items-center">
                  <Award className="w-6 h-6 text-primary-600 mr-3" />
                  <div>
                    <h5 className="font-semibold text-secondary-900 mb-1">
                      Engineering Foundation
                    </h5>
                    <p className="text-secondary-600">
                      The engineering background provides a systematic approach to problem-solving 
                      and technical challenges, which is invaluable in mobile app development and 
                      software architecture design.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Additional Learning */}
          <div className="mt-12 text-center">
            <div className="bg-white rounded-xl p-8 shadow-lg">
              <h3 className="text-2xl font-bold text-secondary-900 mb-4">
                Continuous Professional Development
              </h3>
              <p className="text-lg text-secondary-600 max-w-3xl mx-auto leading-relaxed">
                Beyond formal education, I continuously enhance my skills through online courses, 
                workshops, and hands-on projects. I stay updated with the latest trends in mobile 
                development, Flutter framework updates, and industry best practices to ensure 
                I deliver cutting-edge solutions.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Education;
