import React from "react";
import { ArrowDown, Download, Mail, Phone } from "lucide-react";

const Hero: React.FC = () => {
  const scrollToAbout = () => {
    const aboutSection = document.querySelector("#about");
    if (aboutSection) {
      aboutSection.scrollIntoView({ behavior: "smooth" });
    }
  };

  return (
    <section
      id="home"
      className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-blue-50 relative overflow-hidden"
    >
      {/* Background decoration */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
        <div className="absolute top-40 left-40 w-80 h-80 bg-purple-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>
      </div>

      <div className="container-custom relative z-10">
        <div className="text-center">
          {/* Profile Image Placeholder */}
          <div className="mb-8">
            <div className="w-32 h-32 md:w-40 md:h-40 mx-auto rounded-full bg-gradient-to-r from-blue-500 to-indigo-500 flex items-center justify-center text-white text-4xl md:text-5xl font-bold shadow-xl">
              EP
            </div>
          </div>

          {/* Main Content */}
          <div className="max-w-4xl mx-auto">
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-slate-900 dark:text-white mb-6 animate-fade-in">
              Hi, I'm <span className="text-gradient">EDOBOR PRAISE</span>
            </h1>

            <h2 className="text-xl md:text-2xl lg:text-3xl text-slate-600 dark:text-gray-300 mb-8 animate-slide-up">
              Flutter Developer & Mobile App Specialist
            </h2>

            <p className="text-lg md:text-xl text-slate-600 dark:text-gray-300 mb-12 max-w-3xl mx-auto leading-relaxed animate-slide-up">
              Results-driven Flutter Developer with 4-5 years of experience in
              building high-performance, cross-platform mobile applications.
              Passionate about clean code, UI/UX best practices, and delivering
              scalable mobile solutions.
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12 animate-slide-up">
              <a
                href="#projects"
                onClick={(e) => {
                  e.preventDefault();
                  const projectsSection = document.querySelector("#projects");
                  if (projectsSection) {
                    projectsSection.scrollIntoView({ behavior: "smooth" });
                  }
                }}
                className="btn-primary"
              >
                View My Work
              </a>

              <a
                href="#contact"
                onClick={(e) => {
                  e.preventDefault();
                  const contactSection = document.querySelector("#contact");
                  if (contactSection) {
                    contactSection.scrollIntoView({ behavior: "smooth" });
                  }
                }}
                className="btn-secondary"
              >
                <Download className="w-5 h-5" />
                Get Resume
              </a>
            </div>

            {/* Social Links */}
            <div className="flex justify-center space-x-6 mb-12 animate-slide-up">
              <a
                href="https://github.com/obiaderi"
                target="_blank"
                rel="noopener noreferrer"
                className="p-3 bg-white dark:bg-gray-800 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 text-slate-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 flex items-center justify-center"
              >
                <span className="text-sm font-semibold">GH</span>
              </a>

              <a
                href="https://www.linkedin.com/in/edobor-obiaderi-9933b0241"
                target="_blank"
                rel="noopener noreferrer"
                className="p-3 bg-white dark:bg-gray-800 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 text-slate-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 flex items-center justify-center"
              >
                <span className="text-sm font-semibold">LI</span>
              </a>

              <a
                href="mailto:<EMAIL>"
                className="p-3 bg-white dark:bg-gray-800 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 text-slate-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400"
              >
                <Mail className="w-6 h-6" />
              </a>

              <a
                href="tel:+2349167392396"
                className="p-3 bg-white dark:bg-gray-800 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 text-slate-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400"
              >
                <Phone className="w-6 h-6" />
              </a>
            </div>
          </div>

          {/* Scroll indicator */}
          <button
            onClick={scrollToAbout}
            className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce-slow"
          >
            <ArrowDown className="w-6 h-6 text-slate-400 dark:text-gray-500" />
          </button>
        </div>
      </div>
    </section>
  );
};

export default Hero;
