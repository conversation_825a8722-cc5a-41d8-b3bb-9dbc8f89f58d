import React from "react";
import {
  ExternalLink,
  Github,
  Smartphone,
  CreditCard,
  ShoppingBag,
  Heart,
  Book,
  Gamepad2,
} from "lucide-react";
import AnimatedSection from "./AnimatedSection";

const Projects: React.FC = () => {
  const projects = [
    {
      title: "Korrency",
      subtitle: "Send money easily (Fintech)",
      description:
        "A comprehensive fintech mobile application that enables users to send money easily and securely. Built with Flutter, featuring real-time transactions, secure authentication, and intuitive user interface.",
      icon: <CreditCard className="w-8 h-8" />,
      technologies: ["Flutter", "Dart", "Firebase", "RESTful APIs", "BLoC"],
      playStoreUrl:
        "https://play.google.com/store/apps/details?id=korrency.mobile.com",
      category: "Fintech",
    },
    {
      title: "VitalSwapp LLC",
      subtitle: "USA Fintech Platform",
      description:
        "A sophisticated fintech platform for the US market, providing comprehensive financial services and secure transaction capabilities with advanced user management and analytics.",
      icon: <CreditCard className="w-8 h-8" />,
      technologies: ["Flutter", "Dart", "Firebase", "GraphQL", "Provider"],
      playStoreUrl:
        "https://play.google.com/store/apps/details?id=com.swap.swap",
      category: "Fintech",
    },
    {
      title: "FoodCourt App",
      subtitle: "Food Delivery Platform",
      description:
        "A complete food delivery application connecting customers with restaurants. Features include real-time order tracking, payment integration, and restaurant management system.",
      icon: <ShoppingBag className="w-8 h-8" />,
      technologies: ["Flutter", "Dart", "Firebase", "Google Maps", "Stripe"],
      playStoreUrl:
        "https://play.google.com/store/apps/details?id=com.cokitchen.foodcourt",
      category: "Food Delivery",
    },
    {
      title: "Beauty Hut",
      subtitle: "Ecommerce Fashion Platform",
      description:
        "An elegant e-commerce mobile application for fashion and beauty products. Includes product catalog, shopping cart, secure payments, and order management.",
      icon: <Heart className="w-8 h-8" />,
      technologies: ["Flutter", "Dart", "Firebase", "Stripe", "Riverpod"],
      playStoreUrl:
        "https://play.google.com/store/apps/details?id=com.beautyhut.beautyhut_mobile",
      category: "E-commerce",
    },
    {
      title: "Vibe360",
      subtitle: "Virtual Health Space",
      description:
        "A comprehensive virtual health platform providing telemedicine services, health tracking, and wellness programs with secure patient data management.",
      icon: <Heart className="w-8 h-8" />,
      technologies: ["Flutter", "Dart", "Firebase", "WebRTC", "BLoC"],
      playStoreUrl:
        "https://play.google.com/store/apps/details?id=com.vibes360.app",
      category: "Healthcare",
    },
    {
      title: "Blockroll",
      subtitle: "Blockchain Fintech",
      description:
        "A cutting-edge blockchain-based fintech application offering cryptocurrency transactions, wallet management, and decentralized financial services.",
      icon: <Gamepad2 className="w-8 h-8" />,
      technologies: ["Flutter", "Dart", "Blockchain APIs", "Web3", "Provider"],
      websiteUrl: "https://www.blockroll.app",
      category: "Blockchain",
    },
    {
      title: "LITPAD",
      subtitle: "Book Reading Platform",
      description:
        "A comprehensive digital book reading platform with features like offline reading, bookmarks, reading progress tracking, and social reading features.",
      icon: <Book className="w-8 h-8" />,
      technologies: ["Flutter", "Dart", "Firebase", "SQLite", "BLoC"],
      websiteUrl: "https://join.litpad.app/",
      category: "Education",
    },
  ];

  const ProjectCard: React.FC<{ project: (typeof projects)[0] }> = ({
    project,
  }) => (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden card-hover">
      <div className="p-8">
        {/* Header */}
        <div className="flex items-start justify-between mb-6">
          <div className="flex items-center">
            <div className="text-primary-600 dark:text-blue-400 mr-4">
              {project.icon}
            </div>
            <div>
              <h3 className="text-xl font-bold text-secondary-900 dark:text-white mb-1">
                {project.title}
              </h3>
              <p className="text-primary-600 dark:text-blue-400 font-medium">
                {project.subtitle}
              </p>
            </div>
          </div>
          <span className="bg-primary-100 dark:bg-blue-900 text-primary-700 dark:text-blue-300 px-3 py-1 rounded-full text-sm font-medium">
            {project.category}
          </span>
        </div>

        {/* Description */}
        <p className="text-secondary-600 dark:text-gray-300 mb-6 leading-relaxed">
          {project.description}
        </p>

        {/* Technologies */}
        <div className="mb-6">
          <h4 className="text-sm font-semibold text-secondary-700 dark:text-gray-300 mb-3">
            Technologies Used:
          </h4>
          <div className="flex flex-wrap gap-2">
            {project.technologies.map((tech, index) => (
              <span
                key={index}
                className="bg-gray-100 dark:bg-gray-700 text-secondary-600 dark:text-gray-300 px-3 py-1 rounded-full text-sm"
              >
                {tech}
              </span>
            ))}
          </div>
        </div>

        {/* Links */}
        <div className="flex gap-4">
          {project.playStoreUrl && (
            <a
              href={project.playStoreUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-2 bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg transition-colors duration-200"
            >
              <Smartphone className="w-4 h-4" />
              Play Store
            </a>
          )}
          {project.websiteUrl && (
            <a
              href={project.websiteUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-2 bg-secondary-600 hover:bg-secondary-700 text-white px-4 py-2 rounded-lg transition-colors duration-200"
            >
              <ExternalLink className="w-4 h-4" />
              Website
            </a>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <section
      id="projects"
      className="section-padding bg-white dark:bg-gray-900"
    >
      <div className="container-custom">
        <div className="max-w-7xl mx-auto">
          {/* Section Header */}
          <AnimatedSection className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-slate-900 dark:text-white mb-6">
              Featured <span className="text-gradient">Projects</span>
            </h2>
            <p className="text-lg text-slate-600 dark:text-gray-300 max-w-3xl mx-auto">
              A showcase of my mobile development work across various industries
              including fintech, e-commerce, healthcare, and more
            </p>
          </AnimatedSection>

          {/* Projects Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {projects.map((project, index) => (
              <AnimatedSection
                key={index}
                delay={index * 0.1}
                animation="scaleIn"
              >
                <ProjectCard project={project} />
              </AnimatedSection>
            ))}
          </div>

          {/* Call to Action */}
          <AnimatedSection
            delay={0.4}
            className="text-center bg-gradient-to-r from-primary-50 to-blue-50 dark:from-gray-800 dark:to-gray-700 rounded-2xl p-8 md:p-12"
          >
            <h3 className="text-2xl md:text-3xl font-bold text-secondary-900 dark:text-white mb-6">
              Interested in Working Together?
            </h3>
            <p className="text-lg text-secondary-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto">
              I'm always excited to take on new challenges and create innovative
              mobile solutions. Let's discuss how I can help bring your ideas to
              life.
            </p>
            <a
              href="#contact"
              onClick={(e) => {
                e.preventDefault();
                const contactSection = document.querySelector("#contact");
                if (contactSection) {
                  contactSection.scrollIntoView({ behavior: "smooth" });
                }
              }}
              className="btn-primary"
            >
              Get In Touch
            </a>
          </AnimatedSection>
        </div>
      </div>
    </section>
  );
};

export default Projects;
