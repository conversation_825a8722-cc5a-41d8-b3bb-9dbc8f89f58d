import React from 'react';
import { Download, Eye, FileText } from 'lucide-react';

const Resume: React.FC = () => {
  const resumeUrl = '/resume.pdf'; // Path to resume file in public directory

  const handleViewResume = () => {
    window.open(resumeUrl, '_blank');
  };

  const handleDownloadResume = () => {
    const link = document.createElement('a');
    link.href = resumeUrl;
    link.download = 'Edobor_Praise_Resume.pdf';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <section id="resume" className="section-padding bg-gray-50 dark:bg-gray-900">
      <div className="container-custom">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            My Resume
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Download or view my complete resume to learn more about my experience, 
            skills, and professional background.
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 md:p-12">
            <div className="flex flex-col md:flex-row items-center justify-between gap-8">
              {/* Resume Preview */}
              <div className="flex-1 text-center md:text-left">
                <div className="inline-flex items-center justify-center w-20 h-20 bg-blue-100 dark:bg-blue-900 rounded-full mb-6">
                  <FileText className="w-10 h-10 text-blue-600 dark:text-blue-400" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  Professional Resume
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-6">
                  Get a comprehensive overview of my professional journey, technical skills, 
                  education, and project experience in a detailed PDF format.
                </p>
                
                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-4 justify-center md:justify-start">
                  <button
                    onClick={handleViewResume}
                    className="btn-primary group"
                  >
                    <Eye className="w-5 h-5 group-hover:scale-110 transition-transform" />
                    View Resume
                  </button>
                  <button
                    onClick={handleDownloadResume}
                    className="btn-secondary group"
                  >
                    <Download className="w-5 h-5 group-hover:scale-110 transition-transform" />
                    Download PDF
                  </button>
                </div>
              </div>

              {/* Resume Stats */}
              <div className="flex-shrink-0">
                <div className="grid grid-cols-2 gap-6 text-center">
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">3+</div>
                    <div className="text-sm text-gray-600 dark:text-gray-300">Years Experience</div>
                  </div>
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">15+</div>
                    <div className="text-sm text-gray-600 dark:text-gray-300">Projects Completed</div>
                  </div>
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">10+</div>
                    <div className="text-sm text-gray-600 dark:text-gray-300">Technologies</div>
                  </div>
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">100%</div>
                    <div className="text-sm text-gray-600 dark:text-gray-300">Client Satisfaction</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Resume;
