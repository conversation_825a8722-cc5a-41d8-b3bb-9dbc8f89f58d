import React from "react";
import { Code, Database, Cloud, Wrench } from "lucide-react";

const Skills: React.FC = () => {
  const skillCategories = [
    {
      icon: <Code className="w-8 h-8" />,
      title: "Languages & Frameworks",
      skills: [
        { name: "<PERSON><PERSON>", level: 95 },
        { name: "Flutter", level: 95 },
        { name: "JavaScript", level: 80 },
        { name: "React.js", level: 75 },
      ],
    },
    {
      icon: <Database className="w-8 h-8" />,
      title: "Backend & Database",
      skills: [
        { name: "Firebase", level: 90 },
        { name: "RESTful APIs", level: 90 },
        { name: "GraphQL", level: 75 },
        { name: "SQLite", level: 85 },
      ],
    },
    {
      icon: <Cloud className="w-8 h-8" />,
      title: "State Management & Storage",
      skills: [
        { name: "BLoC", level: 90 },
        { name: "Provider", level: 85 },
        { name: "Riverpod", level: 80 },
        { name: "Hive", level: 85 },
      ],
    },
    {
      icon: <Wrench className="w-8 h-8" />,
      title: "Tools & Technologies",
      skills: [
        { name: "Git & GitHub", level: 90 },
        { name: "Android Studio", level: 95 },
        { name: "VS Code", level: 90 },
        { name: "Figma", level: 70 },
      ],
    },
  ];

  const SkillBar: React.FC<{ skill: { name: string; level: number } }> = ({
    skill,
  }) => (
    <div className="mb-4">
      <div className="flex justify-between items-center mb-2">
        <span className="text-slate-700 font-medium">{skill.name}</span>
        <span className="text-slate-500 text-sm">{skill.level}%</span>
      </div>
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div
          className="bg-gradient-to-r from-blue-500 to-indigo-500 h-2 rounded-full transition-all duration-1000 ease-out"
          style={{ width: `${skill.level}%` }}
        ></div>
      </div>
    </div>
  );

  return (
    <section id="skills" className="section-padding bg-gray-50">
      <div className="container-custom">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-slate-900 mb-6">
              Technical <span className="text-gradient">Skills</span>
            </h2>
            <p className="text-lg text-slate-600 max-w-3xl mx-auto">
              A comprehensive overview of my technical expertise and proficiency
              levels
            </p>
          </div>

          {/* Skills Grid */}
          <div className="grid md:grid-cols-2 gap-8 mb-16">
            {skillCategories.map((category, index) => (
              <div
                key={index}
                className="bg-white rounded-xl p-8 shadow-lg card-hover"
              >
                <div className="flex items-center mb-6">
                  <div className="text-primary-600 mr-4">{category.icon}</div>
                  <h3 className="text-xl font-bold text-secondary-900">
                    {category.title}
                  </h3>
                </div>

                <div className="space-y-4">
                  {category.skills.map((skill, skillIndex) => (
                    <SkillBar key={skillIndex} skill={skill} />
                  ))}
                </div>
              </div>
            ))}
          </div>

          {/* Additional Skills */}
          <div className="bg-white rounded-xl p-8 shadow-lg">
            <h3 className="text-2xl font-bold text-secondary-900 mb-8 text-center">
              Additional Technologies & Tools
            </h3>

            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {[
                "Firebase Auth",
                "Firestore",
                "Cloud Functions",
                "SharedPreferences",
                "Realtime Database",
                "Postman",
                "Cursor IDE",
                "Material Design",
                "Cupertino Design",
                "App Store Connect",
                "Google Play Console",
                "CI/CD",
              ].map((tech, index) => (
                <div
                  key={index}
                  className="bg-gradient-to-r from-primary-50 to-blue-50 rounded-lg p-4 text-center hover:from-primary-100 hover:to-blue-100 transition-colors duration-200"
                >
                  <span className="text-secondary-700 font-medium">{tech}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Certifications or Learning */}
          <div className="mt-16 text-center">
            <div className="bg-gradient-to-r from-primary-600 to-blue-600 rounded-xl p-8 text-white">
              <h3 className="text-2xl font-bold mb-4">Continuous Learning</h3>
              <p className="text-lg opacity-90 max-w-3xl mx-auto">
                I'm committed to staying current with the latest technologies
                and best practices in mobile development. Always exploring new
                tools, frameworks, and methodologies to deliver cutting-edge
                solutions.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Skills;
