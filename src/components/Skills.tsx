import React from "react";
import { Code, Database, <PERSON>, Wrench } from "lucide-react";
import AnimatedSection from "./AnimatedSection";

const Skills: React.FC = () => {
  const skillCategories = [
    {
      icon: <Code className="w-8 h-8" />,
      title: "Languages & Frameworks",
      skills: [
        { name: "Dar<PERSON>", level: 95 },
        { name: "Flutter", level: 95 },
        { name: "JavaScript", level: 80 },
        { name: "React.js", level: 75 },
      ],
    },
    {
      icon: <Database className="w-8 h-8" />,
      title: "Backend & Database",
      skills: [
        { name: "Firebase", level: 90 },
        { name: "RESTful APIs", level: 90 },
        { name: "GraphQL", level: 75 },
        { name: "SQLite", level: 85 },
      ],
    },
    {
      icon: <Cloud className="w-8 h-8" />,
      title: "State Management & Storage",
      skills: [
        { name: "BLoC", level: 90 },
        { name: "Provider", level: 85 },
        { name: "Riverpod", level: 80 },
        { name: "Hive", level: 85 },
      ],
    },
    {
      icon: <Wrench className="w-8 h-8" />,
      title: "Tools & Technologies",
      skills: [
        { name: "Git & GitHub", level: 90 },
        { name: "Android Studio", level: 95 },
        { name: "VS Code", level: 90 },
        { name: "Figma", level: 70 },
      ],
    },
  ];

  const SkillBar: React.FC<{ skill: { name: string; level: number } }> = ({
    skill,
  }) => (
    <div className="mb-4">
      <div className="flex justify-between items-center mb-2">
        <span className="text-slate-700 dark:text-gray-300 font-medium">
          {skill.name}
        </span>
        <span className="text-slate-500 dark:text-gray-400 text-sm">
          {skill.level}%
        </span>
      </div>
      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
        <div
          className="bg-gradient-to-r from-blue-500 to-indigo-500 h-2 rounded-full transition-all duration-1000 ease-out"
          style={{ width: `${skill.level}%` }}
        ></div>
      </div>
    </div>
  );

  return (
    <section
      id="skills"
      className="section-padding bg-gray-50 dark:bg-gray-800"
    >
      <div className="container-custom">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <AnimatedSection className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-slate-900 dark:text-white mb-6">
              Technical <span className="text-gradient">Skills</span>
            </h2>
            <p className="text-lg text-slate-600 dark:text-gray-300 max-w-3xl mx-auto">
              A comprehensive overview of my technical expertise and proficiency
              levels
            </p>
          </AnimatedSection>

          {/* Skills Grid */}
          <div className="grid md:grid-cols-2 gap-8 mb-16">
            {skillCategories.map((category, index) => (
              <AnimatedSection
                key={index}
                delay={index * 0.1}
                animation="scaleIn"
                className="bg-white dark:bg-gray-900 rounded-xl p-8 shadow-lg card-hover"
              >
                <div className="flex items-center mb-6">
                  <div className="text-primary-600 dark:text-blue-400 mr-4">
                    {category.icon}
                  </div>
                  <h3 className="text-xl font-bold text-secondary-900 dark:text-white">
                    {category.title}
                  </h3>
                </div>

                <div className="space-y-4">
                  {category.skills.map((skill, skillIndex) => (
                    <SkillBar key={skillIndex} skill={skill} />
                  ))}
                </div>
              </AnimatedSection>
            ))}
          </div>

          {/* Additional Skills */}
          <AnimatedSection
            delay={0.4}
            className="bg-white dark:bg-gray-900 rounded-xl p-8 shadow-lg"
          >
            <h3 className="text-2xl font-bold text-secondary-900 dark:text-white mb-8 text-center">
              Additional Technologies & Tools
            </h3>

            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {[
                "Firebase Auth",
                "Firestore",
                "Cloud Functions",
                "SharedPreferences",
                "Realtime Database",
                "Postman",
                "Cursor IDE",
                "Material Design",
                "Cupertino Design",
                "App Store Connect",
                "Google Play Console",
                "CI/CD",
              ].map((tech, index) => (
                <div
                  key={index}
                  className="bg-gradient-to-r from-primary-50 to-blue-50 dark:from-gray-700 dark:to-gray-600 rounded-lg p-4 text-center hover:from-primary-100 hover:to-blue-100 dark:hover:from-gray-600 dark:hover:to-gray-500 transition-colors duration-200"
                >
                  <span className="text-secondary-700 dark:text-gray-200 font-medium">
                    {tech}
                  </span>
                </div>
              ))}
            </div>
          </AnimatedSection>

          {/* Certifications or Learning */}
          <AnimatedSection delay={0.6} className="mt-16 text-center">
            <div className="bg-gradient-to-r from-primary-600 to-blue-600 rounded-xl p-8 text-white">
              <h3 className="text-2xl font-bold mb-4">Continuous Learning</h3>
              <p className="text-lg opacity-90 max-w-3xl mx-auto">
                I'm committed to staying current with the latest technologies
                and best practices in mobile development. Always exploring new
                tools, frameworks, and methodologies to deliver cutting-edge
                solutions.
              </p>
            </div>
          </AnimatedSection>
        </div>
      </div>
    </section>
  );
};

export default Skills;
